import tkinter as tk
from tkinter import ttk
import datetime

class PhoneApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()

    def setup_window(self):
        # إعداد نافذة بحجم الهاتف (360x640 pixels - حجم شائع للهواتف)
        self.root.title("تطبيق الهاتف")
        self.root.geometry("360x640")
        self.root.resizable(False, False)

        # وضع النافذة في وسط الشاشة
        self.root.eval('tk::PlaceWindow . center')

        # تعيين لون الخلفية
        self.root.configure(bg='#f0f0f0')

    def create_widgets(self):
        # شريط العنوان
        title_frame = tk.Frame(self.root, bg='#2196F3', height=60)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="مرحباً بك",
                              font=('Arial', 16, 'bold'),
                              fg='white', bg='#2196F3')
        title_label.pack(expand=True)

        # منطقة المحتوى الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عرض الوقت والتاريخ
        self.time_label = tk.Label(main_frame,
                                  font=('Arial', 20, 'bold'),
                                  bg='#f0f0f0', fg='#333')
        self.time_label.pack(pady=10)

        self.date_label = tk.Label(main_frame,
                                  font=('Arial', 12),
                                  bg='#f0f0f0', fg='#666')
        self.date_label.pack(pady=5)

        # أزرار التطبيق
        buttons_frame = tk.Frame(main_frame, bg='#f0f0f0')
        buttons_frame.pack(pady=30)

        # إنشاء أزرار بتصميم جميل
        self.create_button(buttons_frame, "الرسائل", "#4CAF50", 0, 0)
        self.create_button(buttons_frame, "الاتصالات", "#FF9800", 0, 1)
        self.create_button(buttons_frame, "الكاميرا", "#9C27B0", 1, 0)
        self.create_button(buttons_frame, "الإعدادات", "#607D8B", 1, 1)

        # منطقة المعلومات
        info_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        info_frame.pack(fill='x', pady=20)

        info_label = tk.Label(info_frame,
                             text="تطبيق تجريبي بحجم الهاتف\nتم إنشاؤه باستخدام Python و Tkinter",
                             font=('Arial', 10),
                             bg='white', fg='#333',
                             justify='center')
        info_label.pack(pady=15)

        # شريط الحالة السفلي
        status_frame = tk.Frame(self.root, bg='#333', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        status_label = tk.Label(status_frame, text="جاهز",
                               font=('Arial', 8),
                               fg='white', bg='#333')
        status_label.pack(side='left', padx=10, pady=5)

        # تحديث الوقت
        self.update_time()

    def create_button(self, parent, text, color, row, col):
        button = tk.Button(parent, text=text,
                          font=('Arial', 12, 'bold'),
                          bg=color, fg='white',
                          width=8, height=2,
                          relief='flat',
                          command=lambda: self.button_clicked(text))
        button.grid(row=row, column=col, padx=10, pady=10)

        # تأثير hover
        def on_enter(e):
            button.configure(relief='raised')
        def on_leave(e):
            button.configure(relief='flat')

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

    def button_clicked(self, button_name):
        print(f"تم الضغط على زر: {button_name}")
        # يمكنك إضافة وظائف مختلفة لكل زر هنا

    def update_time(self):
        now = datetime.datetime.now()
        time_str = now.strftime("%H:%M:%S")
        date_str = now.strftime("%Y/%m/%d")

        self.time_label.config(text=time_str)
        self.date_label.config(text=date_str)

        # تحديث كل ثانية
        self.root.after(1000, self.update_time)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = PhoneApp()
    app.run()